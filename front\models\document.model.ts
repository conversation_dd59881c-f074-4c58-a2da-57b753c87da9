import { StatutDocument, TypeDemandeDocument } from './enums';
import { Societe } from './societe.model';
import { Applicationuser } from './user.model';

export interface TypeDocument {
  id: string;
  nom: string;
  description?: string;
  societeId: string;
  
  // Relations (optionnelles)
  societe?: Societe;
  documents?: Document[];
}

export interface Document {
  id: string;
  titre: string;
  contenu: string;
  dateCreation: Date;
  dateModification?: Date;
  statut: StatutDocument;
  typeDemande: TypeDemandeDocument;
  typeDocumentId: string;
  societeId: string;
  utilisateurDemandeurId?: string;
  utilisateurApprobateurId?: string;
  
  // Relations (optionnelles)
  typeDocument?: TypeDocument;
  societe?: Societe;
  utilisateurDemandeur?: Applicationuser;
  utilisateurApprobateur?: Applicationuser;
}

// DTO pour la création de type de document
export interface TypeDocumentCreateDto {
  nom: string;
  description?: string;
}

// DTO pour la modification de type de document
export interface TypeDocumentUpdateDto {
  id: string;
  nom: string;
  description?: string;
}

// DTO pour la création de document
export interface DocumentCreateDto {
  titre: string;
  contenu: string;
  typeDemande: TypeDemandeDocument;
  typeDocumentId: string;
}

// DTO pour la modification de document
export interface DocumentUpdateDto {
  id: string;
  titre: string;
  contenu: string;
  typeDemande: TypeDemandeDocument;
  typeDocumentId: string;
}

// DTO pour l'approbation/rejet de document
export interface DocumentApprovalDto {
  id: string;
  statut: StatutDocument;
  commentaire?: string;
}

// DTO pour les filtres de recherche de documents
export interface DocumentFilterDto {
  titre?: string;
  statut?: StatutDocument;
  typeDemande?: TypeDemandeDocument;
  typeDocumentId?: string;
  utilisateurDemandeurId?: string;
  dateCreationDebut?: Date;
  dateCreationFin?: Date;
}

// Interface pour les statistiques de documents
export interface DocumentStatsDto {
  totalDocuments: number;
  documentsEnAttente: number;
  documentsApprouves: number;
  documentsRejetes: number;
  demandesParType: {
    type: TypeDemandeDocument;
    nombre: number;
  }[];
  tempsTraitementMoyen: number; // en heures
}

// Interface pour l'historique des documents
export interface DocumentHistoryDto {
  id: string;
  documentId: string;
  action: string;
  utilisateurId: string;
  utilisateurNom: string;
  date: Date;
  commentaire?: string;
  ancienneValeur?: string;
  nouvelleValeur?: string;
}
