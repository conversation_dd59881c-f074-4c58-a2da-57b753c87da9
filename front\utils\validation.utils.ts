// ===== UTILITAIRES DE VALIDATION =====

/**
 * Valide une adresse email
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Valide un numéro de téléphone français
 */
export function isValidPhoneNumber(phone: string): boolean {
  // Accepte les formats: 01.23.45.67.89, 01 23 45 67 89, 0123456789, +33123456789
  const phoneRegex = /^(?:(?:\+33|0)[1-9](?:[-.\s]?\d{2}){4})$/;
  return phoneRegex.test(phone.replace(/\s/g, ''));
}

/**
 * Valide un matricule fiscal tunisien
 */
export function isValidMatriculeFiscale(matricule: string): boolean {
  // Format tunisien: 7 chiffres + lettre + 3 chiffres + lettre + 3 chiffres
  const matriculeRegex = /^\d{7}[A-Z]\d{3}[A-Z]\d{3}$/;
  return matriculeRegex.test(matricule.toUpperCase());
}

/**
 * Valide la force d'un mot de passe
 */
export interface PasswordStrength {
  isValid: boolean;
  score: number; // 0-4
  feedback: string[];
}

export function validatePasswordStrength(password: string): PasswordStrength {
  const feedback: string[] = [];
  let score = 0;
  
  if (password.length < 8) {
    feedback.push('Le mot de passe doit contenir au moins 8 caractères');
  } else {
    score++;
  }
  
  if (!/[a-z]/.test(password)) {
    feedback.push('Le mot de passe doit contenir au moins une lettre minuscule');
  } else {
    score++;
  }
  
  if (!/[A-Z]/.test(password)) {
    feedback.push('Le mot de passe doit contenir au moins une lettre majuscule');
  } else {
    score++;
  }
  
  if (!/\d/.test(password)) {
    feedback.push('Le mot de passe doit contenir au moins un chiffre');
  } else {
    score++;
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    feedback.push('Le mot de passe doit contenir au moins un caractère spécial');
  } else {
    score++;
  }
  
  return {
    isValid: score >= 4,
    score,
    feedback
  };
}

/**
 * Valide un montant (nombre positif avec maximum 2 décimales)
 */
export function isValidAmount(amount: number): boolean {
  return amount >= 0 && Number.isFinite(amount) && 
         (amount * 100) % 1 === 0; // Vérifie max 2 décimales
}

/**
 * Valide un numéro de facture
 */
export function isValidFactureNumber(numero: string): boolean {
  // Format: lettres, chiffres, tirets, underscores autorisés
  const numeroRegex = /^[A-Za-z0-9_-]+$/;
  return numeroRegex.test(numero) && numero.length >= 3 && numero.length <= 20;
}

/**
 * Valide une date (ne doit pas être dans le futur pour certains cas)
 */
export function isValidDate(date: Date, allowFuture: boolean = true): boolean {
  if (!date || isNaN(date.getTime())) {
    return false;
  }
  
  if (!allowFuture && date > new Date()) {
    return false;
  }
  
  return true;
}

/**
 * Valide une plage de dates
 */
export function isValidDateRange(startDate: Date, endDate: Date): boolean {
  if (!isValidDate(startDate) || !isValidDate(endDate)) {
    return false;
  }
  
  return startDate <= endDate;
}

/**
 * Nettoie et valide un nom (supprime les espaces en trop, vérifie la longueur)
 */
export function validateAndCleanName(name: string): { isValid: boolean; cleanedName: string; error?: string } {
  const cleanedName = name.trim().replace(/\s+/g, ' ');
  
  if (cleanedName.length < 2) {
    return { isValid: false, cleanedName, error: 'Le nom doit contenir au moins 2 caractères' };
  }
  
  if (cleanedName.length > 100) {
    return { isValid: false, cleanedName, error: 'Le nom ne peut pas dépasser 100 caractères' };
  }
  
  // Vérifie que le nom ne contient que des lettres, espaces, tirets et apostrophes
  const nameRegex = /^[a-zA-ZÀ-ÿ\s'-]+$/;
  if (!nameRegex.test(cleanedName)) {
    return { isValid: false, cleanedName, error: 'Le nom contient des caractères non autorisés' };
  }
  
  return { isValid: true, cleanedName };
}

/**
 * Valide une adresse
 */
export function isValidAddress(address: string): boolean {
  const cleanedAddress = address.trim();
  return cleanedAddress.length >= 10 && cleanedAddress.length <= 200;
}

/**
 * Valide un GUID
 */
export function isValidGuid(guid: string): boolean {
  const guidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return guidRegex.test(guid);
}
