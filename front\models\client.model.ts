import { StatutClient } from './enums';
import { Societe } from './societe.model';
import { FactureVente } from './facture-vente.model';

export interface Client {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse: string;
  statut: StatutClient;
  dateCreation: Date;
  societeId: string;
  
  // Relations (optionnelles)
  societe?: Societe;
  facturesVente?: FactureVente[];
}

// DTO pour la création de client
export interface ClientCreateDto {
  nom: string;
  email: string;
  telephone?: string;
  adresse: string;
  statut?: StatutClient;
}

// DTO pour la modification de client
export interface ClientUpdateDto {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse: string;
  statut: StatutClient;
}

// DTO pour les filtres de recherche
export interface ClientFilterDto {
  nom?: string;
  email?: string;
  statut?: StatutClient;
  dateCreationDebut?: Date;
  dateCreationFin?: Date;
}

// Interface pour les statistiques clients
export interface ClientStatsDto {
  totalClients: number;
  clientsActifs: number;
  clientsInactifs: number;
  clientsSuspendus: number;
  nouveauxClientsCeMois: number;
}
