import { StatutFacture } from './enums';
import { Societe } from './societe.model';
import { Applicationuser } from './user.model';

// Interface de base pour toutes les factures
export interface FactureBase {
  id: string;
  numero: string;
  montant: number;
  date: Date;
  dateEcheance?: Date;
  statut: StatutFacture;
  dateEnvoi?: Date;
  datePaiement?: Date;
  notesInternes?: string;
  matriculeFiscaleSociete: string;
  societeId: string;
  utilisateurCreationId?: string;
  dateCreation: Date;
  dateModification?: Date;
  
  // Relations (optionnelles)
  societe?: Societe;
  utilisateurCreation?: Applicationuser;
}

// DTO de base pour la création de factures
export interface FactureCreateBaseDto {
  numero: string;
  date: Date;
  montant: number;
  statut?: StatutFacture;
  dateEcheance?: Date;
  notesInternes?: string;
}

// DTO de base pour la modification de factures
export interface FactureUpdateBaseDto {
  id: string;
  numero: string;
  date: Date;
  montant: number;
  statut: StatutFacture;
  dateEcheance?: Date;
  notesInternes?: string;
}

// DTO pour les filtres de recherche de factures
export interface FactureFilterDto {
  numero?: string;
  montantMin?: number;
  montantMax?: number;
  dateDebut?: Date;
  dateFin?: Date;
  statut?: StatutFacture;
  utilisateurCreationId?: string;
}

// Interface pour les statistiques de factures
export interface FactureStatsDto {
  totalFactures: number;
  facturesBrouillon: number;
  facturesEnvoyees: number;
  facturesPayees: number;
  facturesEnRetard: number;
  facturesAnnulees: number;
  montantTotal: number;
  montantPaye: number;
  montantEnAttente: number;
}
