﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace stage.Migrations
{
    /// <inheritdoc />
    public partial class initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AuditLogs");

            migrationBuilder.DropTable(
                name: "NotificationPreferences");

            migrationBuilder.DropTable(
                name: "ProfilsUtilisateurs");

            migrationBuilder.DropColumn(
                name: "Commentaires",
                table: "RetenuesSource");

            migrationBuilder.DropColumn(
                name: "DateLecture",
                table: "Notifications");

            migrationBuilder.DropColumn(
                name: "DonneesMetadata",
                table: "Notifications");

            migrationBuilder.DropColumn(
                name: "DateModification",
                table: "Fournisseurs");

            migrationBuilder.DropColumn(
                name: "DerniereCommande",
                table: "Fournisseurs");

            migrationBuilder.DropColumn(
                name: "MontantTotalCommandes",
                table: "Fournisseurs");

            migrationBuilder.DropColumn(
                name: "NombreCommandes",
                table: "Fournisseurs");

            migrationBuilder.DropColumn(
                name: "NotesInternes",
                table: "Fournisseurs");

            migrationBuilder.DropColumn(
                name: "CommentairesApprobation",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "EmailCopie",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "EstUrgent",
                table: "Documents");

            migrationBuilder.DropColumn(
                name: "ChiffreAffaireTotal",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "DateModification",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "DernierePaiement",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "NombreFactures",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "NotesInternes",
                table: "Clients");

            migrationBuilder.DropColumn(
                name: "TelephoneMobile",
                table: "Clients");

            migrationBuilder.AddColumn<string>(
                name: "CheminFichier",
                table: "FacturesAchat",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateUpload",
                table: "FacturesAchat",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NomFichierOriginal",
                table: "FacturesAchat",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "TailleFichier",
                table: "FacturesAchat",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TypeFichier",
                table: "FacturesAchat",
                type: "nvarchar(max)",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CheminFichier",
                table: "FacturesAchat");

            migrationBuilder.DropColumn(
                name: "DateUpload",
                table: "FacturesAchat");

            migrationBuilder.DropColumn(
                name: "NomFichierOriginal",
                table: "FacturesAchat");

            migrationBuilder.DropColumn(
                name: "TailleFichier",
                table: "FacturesAchat");

            migrationBuilder.DropColumn(
                name: "TypeFichier",
                table: "FacturesAchat");

            migrationBuilder.AddColumn<string>(
                name: "Commentaires",
                table: "RetenuesSource",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateLecture",
                table: "Notifications",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DonneesMetadata",
                table: "Notifications",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateModification",
                table: "Fournisseurs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DerniereCommande",
                table: "Fournisseurs",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "MontantTotalCommandes",
                table: "Fournisseurs",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<int>(
                name: "NombreCommandes",
                table: "Fournisseurs",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "NotesInternes",
                table: "Fournisseurs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CommentairesApprobation",
                table: "Documents",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EmailCopie",
                table: "Documents",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "EstUrgent",
                table: "Documents",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "ChiffreAffaireTotal",
                table: "Clients",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "DateModification",
                table: "Clients",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DernierePaiement",
                table: "Clients",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "NombreFactures",
                table: "Clients",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "NotesInternes",
                table: "Clients",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TelephoneMobile",
                table: "Clients",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AuditLogs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SocieteId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UtilisateurId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    Action = table.Column<int>(type: "int", nullable: false),
                    AdresseIP = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateAction = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Details = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EntiteId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    NomEntite = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TypeEntite = table.Column<int>(type: "int", nullable: false),
                    UserAgent = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ValeurApres = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ValeurAvant = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AuditLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AuditLogs_AspNetUsers_UtilisateurId",
                        column: x => x.UtilisateurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_AuditLogs_Societes_SocieteId",
                        column: x => x.SocieteId,
                        principalTable: "Societes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "NotificationPreferences",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UtilisateurId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Canal = table.Column<int>(type: "int", nullable: false),
                    EstActive = table.Column<bool>(type: "bit", nullable: false),
                    TypeNotification = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotificationPreferences", x => x.Id);
                    table.ForeignKey(
                        name: "FK_NotificationPreferences_AspNetUsers_UtilisateurId",
                        column: x => x.UtilisateurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ProfilsUtilisateurs",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UtilisateurId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Adresse = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AlertesConnexion = table.Column<bool>(type: "bit", nullable: false),
                    CachetNumerique = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateEmbauche = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Departement = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DeuxFacteursActive = table.Column<bool>(type: "bit", nullable: false),
                    DevisePreferee = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FormatDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LanguePreferee = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PhotoProfil = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Poste = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SignatureNumerique = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Telephone = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TelephoneMobile = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ThemeInterface = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    TimeoutSession = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ProfilsUtilisateurs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ProfilsUtilisateurs_AspNetUsers_UtilisateurId",
                        column: x => x.UtilisateurId,
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_SocieteId",
                table: "AuditLogs",
                column: "SocieteId");

            migrationBuilder.CreateIndex(
                name: "IX_AuditLogs_UtilisateurId",
                table: "AuditLogs",
                column: "UtilisateurId");

            migrationBuilder.CreateIndex(
                name: "IX_NotificationPreferences_UtilisateurId",
                table: "NotificationPreferences",
                column: "UtilisateurId");

            migrationBuilder.CreateIndex(
                name: "IX_ProfilsUtilisateurs_UtilisateurId",
                table: "ProfilsUtilisateurs",
                column: "UtilisateurId",
                unique: true);
        }
    }
}
