import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Societe } from '../models';

@Injectable({
  providedIn: 'root'
})
export class SocieteService {
  private readonly API_URL = '/api/societes';

  constructor(private http: HttpClient) {}

  getSocietes(): Observable<Societe[]> {
    return this.http.get<Societe[]>(this.API_URL);
  }

  getSociete(id: string): Observable<Societe> {
    return this.http.get<Societe>(`${this.API_URL}/${id}`);
  }

  createSociete(societe: Partial<Societe>): Observable<Societe> {
    return this.http.post<Societe>(this.API_URL, societe);
  }

  updateSociete(id: string, societe: Partial<Societe>): Observable<Societe> {
    return this.http.put<Societe>(`${this.API_URL}/${id}`, societe);
  }

  deleteSociete(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  uploadLogo(id: string, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('logo', file);
    return this.http.post(`${this.API_URL}/${id}/logo`, formData);
  }

  uploadSignature(id: string, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('signature', file);
    return this.http.post(`${this.API_URL}/${id}/signature`, formData);
  }

  uploadCachet(id: string, file: File): Observable<any> {
    const formData = new FormData();
    formData.append('cachet', file);
    return this.http.post(`${this.API_URL}/${id}/cachet`, formData);
  }
}
