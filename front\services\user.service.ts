import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Applicationuser } from '../models';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly API_URL = '/api/utilisateurs';

  constructor(private http: HttpClient) {}

  getUtilisateurs(): Observable<Applicationuser[]> {
    return this.http.get<Applicationuser[]>(this.API_URL);
  }

  getUtilisateur(id: string): Observable<Applicationuser> {
    return this.http.get<Applicationuser>(`${this.API_URL}/${id}`);
  }

  createUtilisateur(userData: any): Observable<Applicationuser> {
    return this.http.post<Applicationuser>(this.API_URL, userData);
  }

  updateUtilisateur(id: string, userData: Partial<Applicationuser>): Observable<Applicationuser> {
    return this.http.put<Applicationuser>(`${this.API_URL}/${id}`, userData);
  }

  deleteUtilisateur(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  changePassword(currentPassword: string, newPassword: string): Observable<any> {
    return this.http.post(`${this.API_URL}/change-password`, {
      currentPassword,
      newPassword
    });
  }

  resetPassword(email: string): Observable<any> {
    return this.http.post(`${this.API_URL}/reset-password`, { email });
  }

  confirmResetPassword(token: string, newPassword: string): Observable<any> {
    return this.http.post(`${this.API_URL}/confirm-reset-password`, {
      token,
      newPassword
    });
  }

  updateProfile(profileData: Partial<Applicationuser>): Observable<Applicationuser> {
    return this.http.put<Applicationuser>(`${this.API_URL}/profile`, profileData);
  }

  searchUtilisateurs(searchTerm: string): Observable<Applicationuser[]> {
    const params = new HttpParams().set('search', searchTerm);
    return this.http.get<Applicationuser[]>(`${this.API_URL}/search`, { params });
  }

  getUtilisateursBySociete(societeId: string): Observable<Applicationuser[]> {
    const params = new HttpParams().set('societeId', societeId);
    return this.http.get<Applicationuser[]>(this.API_URL, { params });
  }

  assignRole(userId: string, role: string): Observable<any> {
    return this.http.post(`${this.API_URL}/${userId}/assign-role`, { role });
  }

  getUserRoles(userId: string): Observable<string[]> {
    return this.http.get<string[]>(`${this.API_URL}/${userId}/roles`);
  }
}
