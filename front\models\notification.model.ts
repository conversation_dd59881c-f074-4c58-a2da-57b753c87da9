import { TypeNotification, StatutNotification } from './enums';
import { Societe } from './societe.model';
import { Applicationuser } from './user.model';

export interface Notification {
  id: string;
  utilisateurId: string;
  societeId: string;
  type: TypeNotification;
  titre: string;
  message: string;
  lienAction?: string;
  dateCreation: Date;
  dateLecture?: Date;
  statut: StatutNotification;
  utilisateur?: Applicationuser;
  societe?: Societe;
}
