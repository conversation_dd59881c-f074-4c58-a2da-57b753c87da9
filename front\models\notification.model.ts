import { TypeNotification, StatutNotification } from './enums';
import { Societe } from './societe.model';
import { Applicationuser } from './user.model';

export interface Notification {
  id: string;
  utilisateurId: string;
  societeId: string;
  type: TypeNotification;
  titre: string;
  message: string;
  lienAction?: string;
  dateCreation: Date;
  dateLecture?: Date;
  statut: StatutNotification;
  
  // Relations (optionnelles)
  utilisateur?: Applicationuser;
  societe?: Societe;
}

// DTO pour la création de notification
export interface NotificationCreateDto {
  utilisateurId: string;
  type: TypeNotification;
  titre: string;
  message: string;
  lienAction?: string;
}

// DTO pour marquer comme lue
export interface NotificationMarkReadDto {
  notificationIds: string[];
}

// DTO pour les filtres de notifications
export interface NotificationFilterDto {
  type?: TypeNotification;
  statut?: StatutNotification;
  dateDebut?: Date;
  dateFin?: Date;
  utilisateurId?: string;
}

// Interface pour le compteur de notifications
export interface NotificationCountDto {
  total: number;
  nonLues: number;
  parType: {
    type: TypeNotification;
    nombre: number;
    nonLues: number;
  }[];
}

// Interface pour les paramètres de notification
export interface NotificationSettingsDto {
  utilisateurId: string;
  emailNotifications: boolean;
  pushNotifications: boolean;
  notificationTypes: {
    type: TypeNotification;
    enabled: boolean;
    email: boolean;
    push: boolean;
  }[];
}

// Interface pour l'historique des notifications
export interface NotificationHistoryDto {
  id: string;
  utilisateurId: string;
  type: TypeNotification;
  titre: string;
  dateCreation: Date;
  dateLecture?: Date;
  tempsLecture?: number; // en minutes
  actionEffectuee: boolean;
}

// Interface pour les statistiques de notifications
export interface NotificationStatsDto {
  totalNotifications: number;
  notificationsLues: number;
  notificationsNonLues: number;
  tauxLecture: number; // en pourcentage
  tempsLectureMoyen: number; // en minutes
  notificationsParType: {
    type: TypeNotification;
    nombre: number;
    tauxLecture: number;
  }[];
  notificationsParJour: {
    date: Date;
    nombre: number;
  }[];
}
