// ===== UTILITAIRES POUR LES FICHIERS =====

/**
 * Formate la taille d'un fichier en format lisible
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Obtient l'extension d'un fichier
 */
export function getFileExtension(fileName: string): string {
  return fileName.slice((fileName.lastIndexOf('.') - 1 >>> 0) + 2);
}

/**
 * Obtient le nom du fichier sans extension
 */
export function getFileNameWithoutExtension(fileName: string): string {
  return fileName.replace(/\.[^/.]+$/, '');
}

/**
 * Vérifie si un fichier est une image
 */
export function isImageFile(fileName: string): boolean {
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const extension = getFileExtension(fileName).toLowerCase();
  return imageExtensions.includes(extension);
}

/**
 * Vérifie si un fichier est un PDF
 */
export function isPdfFile(fileName: string): boolean {
  return getFileExtension(fileName).toLowerCase() === 'pdf';
}

/**
 * Vérifie si un fichier est un document Office
 */
export function isOfficeFile(fileName: string): boolean {
  const officeExtensions = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  const extension = getFileExtension(fileName).toLowerCase();
  return officeExtensions.includes(extension);
}

/**
 * Obtient l'icône CSS appropriée pour un type de fichier
 */
export function getFileIcon(fileName: string): string {
  const extension = getFileExtension(fileName).toLowerCase();
  
  if (isImageFile(fileName)) return 'fas fa-image';
  if (isPdfFile(fileName)) return 'fas fa-file-pdf';
  if (extension === 'doc' || extension === 'docx') return 'fas fa-file-word';
  if (extension === 'xls' || extension === 'xlsx') return 'fas fa-file-excel';
  if (extension === 'ppt' || extension === 'pptx') return 'fas fa-file-powerpoint';
  if (extension === 'txt') return 'fas fa-file-alt';
  if (extension === 'zip' || extension === 'rar') return 'fas fa-file-archive';
  
  return 'fas fa-file';
}

/**
 * Convertit un fichier en base64
 */
export function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        // Enlever le préfixe "data:type/subtype;base64,"
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Erreur lors de la conversion en base64'));
      }
    };
    reader.onerror = error => reject(error);
  });
}

/**
 * Télécharge un fichier depuis une URL
 */
export function downloadFile(url: string, fileName: string): void {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Valide un fichier selon les critères donnés
 */
export interface FileValidationOptions {
  maxSize?: number; // en bytes
  allowedExtensions?: string[];
  allowedMimeTypes?: string[];
}

export function validateFile(file: File, options: FileValidationOptions): string | null {
  // Vérifier la taille
  if (options.maxSize && file.size > options.maxSize) {
    return `Le fichier est trop volumineux. Taille maximale autorisée: ${formatFileSize(options.maxSize)}`;
  }
  
  // Vérifier l'extension
  if (options.allowedExtensions) {
    const extension = getFileExtension(file.name).toLowerCase();
    if (!options.allowedExtensions.includes(extension)) {
      return `Extension de fichier non autorisée. Extensions autorisées: ${options.allowedExtensions.join(', ')}`;
    }
  }
  
  // Vérifier le type MIME
  if (options.allowedMimeTypes) {
    if (!options.allowedMimeTypes.includes(file.type)) {
      return `Type de fichier non autorisé. Types autorisés: ${options.allowedMimeTypes.join(', ')}`;
    }
  }
  
  return null; // Fichier valide
}
