﻿namespace stage.Models
{
    public enum StatutFournisseur
    {
        Actif = 0,
        Inactif = 1,
        Suspendu = 2
    }

    public enum CategorieFournisseur
    {
        Materiel = 0,
        Services = 1,
        Equipement = 2,
        Maintenance = 3,
        Autre = 4
    }

    public class Fournisseur
    {
        public Guid Id { get; set; }
        public string Nom { get; set; }
        public string Email { get; set; }
        public string? Telephone { get; set; }
        public string? TelephoneMobile { get; set; }
        public string Adresse { get; set; }
        public StatutFournisseur Statut { get; set; } = StatutFournisseur.Actif;
        public CategorieFournisseur Categorie { get; set; } = CategorieFournisseur.Autre;
        public DateTime DateCreation { get; set; } = DateTime.UtcNow;

        public Guid SocieteId { get; set; }
        public Societe Societe { get; set; }
        public ICollection<FactureAchat>? FacturesAchat { get; set; }
    }

}