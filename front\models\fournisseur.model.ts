import { StatutFournisseur } from './enums';
import { Societe } from './societe.model';
import { FactureAchat } from './facture-achat.model';

export interface Fournisseur {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse: string;
  statut: StatutFournisseur;
  dateCreation: Date;
  societeId: string;
  
  // Relations (optionnelles)
  societe?: Societe;
  facturesAchat?: FactureAchat[];
}

// DTO pour la création de fournisseur
export interface FournisseurCreateDto {
  nom: string;
  email: string;
  telephone?: string;
  adresse: string;
  statut?: StatutFournisseur;
}

// DTO pour la modification de fournisseur
export interface FournisseurUpdateDto {
  id: string;
  nom: string;
  email: string;
  telephone?: string;
  adresse: string;
  statut: StatutFournisseur;
}

// DTO pour les filtres de recherche
export interface FournisseurFilterDto {
  nom?: string;
  email?: string;
  statut?: StatutFournisseur;
  dateCreationDebut?: Date;
  dateCreationFin?: Date;
}

// Interface pour les statistiques fournisseurs
export interface FournisseurStatsDto {
  totalFournisseurs: number;
  fournisseursActifs: number;
  fournisseursInactifs: number;
  fournisseursSuspendus: number;
  nouveauxFournisseursCeMois: number;
}
