# Modèles Frontend Angular - Projet Stage

Ce dossier contient tous les modèles TypeScript pour l'application Angular correspondant à l'API ASP.NET Core.

## 📁 Structure des dossiers

```
front/
├── models/                 # Modèles de données
│   ├── enums.ts           # Énumérations et leurs labels
│   ├── societe.model.ts   # Modèle Société
│   ├── user.model.ts      # Modèles Utilisateur et Auth
│   ├── client.model.ts    # Modèle Client
│   ├── fournisseur.model.ts # Modèle Fournisseur
│   ├── facture-base.model.ts # Modèle de base pour factures
│   ├── facture-achat.model.ts # Modèle Facture d'achat
│   ├── facture-vente.model.ts # Modèle Facture de vente
│   ├── document.model.ts  # Modèles Document et TypeDocument
│   ├── retenue-source.model.ts # Modèle Retenue à la source
│   ├── notification.model.ts # Modèle Notification
│   ├── common.model.ts    # Modèles communs (API, pagination, etc.)
│   └── index.ts          # Export centralisé
├── utils/                 # Utilitaires
│   ├── date.utils.ts     # Utilitaires pour les dates
│   ├── file.utils.ts     # Utilitaires pour les fichiers
│   ├── validation.utils.ts # Utilitaires de validation
│   └── index.ts          # Export centralisé
└── README.md             # Ce fichier
```

## 🎯 Utilisation

### Import des modèles
```typescript
// Import depuis l'index principal
import { 
  FactureAchat, 
  FactureAchatCreateDto, 
  StatutFacture,
  Client,
  ApiResponse 
} from './models';

// Ou import spécifique
import { FactureAchat } from './models/facture-achat.model';
```

### Utilisation dans les services
```typescript
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { FactureAchat, FactureAchatCreateDto, ApiResponse } from '../models';

@Injectable()
export class FactureAchatService {
  constructor(private http: HttpClient) {}

  getFactures(): Observable<FactureAchat[]> {
    return this.http.get<FactureAchat[]>('/api/factures-achat');
  }

  createFacture(facture: FactureAchatCreateDto): Observable<FactureAchat> {
    const formData = new FormData();
    formData.append('numero', facture.numero);
    formData.append('date', facture.date.toISOString());
    formData.append('montant', facture.montant.toString());
    formData.append('fournisseurId', facture.fournisseurId);
    
    if (facture.fichier) {
      formData.append('fichier', facture.fichier);
    }
    
    return this.http.post<FactureAchat>('/api/factures-achat', formData);
  }
}
```

### Utilisation des utilitaires
```typescript
import { 
  formatDateForDisplay, 
  formatFileSize, 
  isValidEmail,
  validatePasswordStrength 
} from './utils';

// Formatage de date
const dateFormatted = formatDateForDisplay(facture.dateCreation);

// Validation d'email
const isEmailValid = isValidEmail('<EMAIL>');

// Validation de mot de passe
const passwordCheck = validatePasswordStrength('MonMotDePasse123!');
```

## 🔄 Correspondances Backend/Frontend

| Backend (C#) | Frontend (TypeScript) |
|--------------|----------------------|
| `Guid` | `string` |
| `DateTime` | `Date` |
| `decimal` | `number` |
| `int` | `number` |
| `bool` | `boolean` |
| `string?` | `string \| undefined` |
| `ICollection<T>` | `T[]` |

## 📝 Conventions de nommage

- **Interfaces** : PascalCase (ex: `FactureAchat`)
- **Propriétés** : camelCase (ex: `dateCreation`)
- **Enums** : PascalCase (ex: `StatutFacture`)
- **DTOs** : Suffixe `Dto` (ex: `FactureAchatCreateDto`)
- **Filtres** : Suffixe `FilterDto` (ex: `ClientFilterDto`)
- **Stats** : Suffixe `StatsDto` (ex: `FactureStatsDto`)

## 🛠️ Fonctionnalités incluses

### Modèles complets
- ✅ Toutes les entités backend
- ✅ DTOs pour CRUD operations
- ✅ Filtres de recherche
- ✅ Interfaces de statistiques
- ✅ Modèles de réponse API

### Utilitaires
- ✅ Gestion des dates (parsing, formatage)
- ✅ Gestion des fichiers (validation, formatage)
- ✅ Validation des données (email, téléphone, etc.)
- ✅ Helpers pour les enums

### Fonctionnalités avancées
- ✅ Pagination
- ✅ Recherche et filtrage
- ✅ Upload de fichiers
- ✅ Gestion d'erreurs
- ✅ Notifications
- ✅ Export de données

## 🚀 Prochaines étapes

1. **Intégrer dans Angular** : Copier ces fichiers dans votre projet Angular
2. **Créer les services** : Implémenter les services HTTP correspondants
3. **Créer les composants** : Développer les interfaces utilisateur
4. **Tests** : Ajouter des tests unitaires pour les utilitaires

## 📋 Notes importantes

- Les dates sont automatiquement converties par Angular HttpClient
- Utilisez les utilitaires de validation avant d'envoyer les données
- Les enums incluent des labels français pour l'affichage
- Tous les modèles respectent la structure de l'API backend
