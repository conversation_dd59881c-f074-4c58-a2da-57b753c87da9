import { Societe } from './societe.model';
import { Applicationuser } from './user.model';

export interface RetenueSource {
  id: string;
  dateScan?: Date;
  cheminFichier?: string;
  societeId: string;
  utilisateurId: string;
  
  // Relations (optionnelles)
  societe?: Societe;
  utilisateur?: Applicationuser;
}

// DTO pour l'upload de retenue à la source
export interface RetenueSourceUploadDto {
  fichier: File;
  dateScan?: Date;
}

// DTO pour la modification de retenue à la source
export interface RetenueSourceUpdateDto {
  id: string;
  dateScan?: Date;
  cheminFichier?: string;
}

// DTO pour les filtres de recherche
export interface RetenueSourceFilterDto {
  utilisateurId?: string;
  dateScanDebut?: Date;
  dateScanFin?: Date;
  avecFichier?: boolean;
}

// Interface pour les informations de fichier
export interface RetenueSourceFileInfo {
  id: string;
  nomFichier: string;
  tailleFichier: number;
  typeFichier: string;
  dateScan: Date;
  urlTelechargement: string;
  utilisateurNom: string;
}

// Interface pour les statistiques des retenues à la source
export interface RetenueSourceStatsDto {
  totalRetenues: number;
  retenuesAvecFichier: number;
  retenuesSansFichier: number;
  retenuesCeMois: number;
  retenuesCetteAnnee: number;
  utilisateurLePlusActif: {
    id: string;
    nom: string;
    nombreRetenues: number;
  };
}

// Interface pour le rapport des retenues à la source
export interface RapportRetenuesSourceDto {
  periode: {
    debut: Date;
    fin: Date;
  };
  retenuesParMois: {
    mois: string;
    nombre: number;
  }[];
  retenuesParUtilisateur: {
    utilisateurId: string;
    utilisateurNom: string;
    nombre: number;
  }[];
  totalFichiers: number;
  tailleTotaleFichiers: number; // en bytes
}
