using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using stage.Models;
using stage.Services;
using System.Security.Claims;

namespace stage.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class NotificationsController : ControllerBase
    {
        private readonly INotificationService _notificationService;

        public NotificationsController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        private Guid GetUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                throw new InvalidOperationException("UserId claim missing or invalid in JWT.");
            }
            return userId;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }



        [HttpGet]
        public async Task<ActionResult<List<Notification>>> GetNotifications([FromQuery] bool includeRead = true)
        {
            var userId = GetUserId();
            var notifications = await _notificationService.GetUserNotificationsAsync(userId, includeRead);
            return Ok(notifications);
        }

        [HttpGet("unread")]
        public async Task<ActionResult<List<Notification>>> GetUnreadNotifications()
        {
            var userId = GetUserId();
            var notifications = await _notificationService.GetUnreadNotificationsAsync(userId);
            return Ok(notifications);
        }

        [HttpGet("unread-count")]
        public async Task<ActionResult<int>> GetUnreadCount()
        {
            var userId = GetUserId();
            var count = await _notificationService.GetUnreadCountAsync(userId);
            return Ok(new { Count = count });
        }

        [HttpPost("{id}/mark-read")]
        public async Task<IActionResult> MarkAsRead(Guid id)
        {
            var userId = GetUserId();
            var societeId = GetUserSocieteId();
            
            var success = await _notificationService.MarkAsReadAsync(id, userId);
            if (!success)
                return NotFound();



            return Ok(new { Message = "Notification marquée comme lue" });
        }

        [HttpPost("mark-all-read")]
        public async Task<IActionResult> MarkAllAsRead()
        {
            var userId = GetUserId();
            var societeId = GetUserSocieteId();
            
            var success = await _notificationService.MarkAllAsReadAsync(userId);
            if (!success)
                return BadRequest("Erreur lors du marquage des notifications");



            return Ok(new { Message = "Toutes les notifications ont été marquées comme lues" });
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteNotification(Guid id)
        {
            var userId = GetUserId();
            var societeId = GetUserSocieteId();
            
            var success = await _notificationService.DeleteNotificationAsync(id, userId);
            if (!success)
                return NotFound();



            return Ok(new { Message = "Notification supprimée" });
        }


    }
}
