// ===== INTERFACES COMMUNES =====

// Réponse API générique
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  timestamp?: Date;
}

// Réponse paginée
export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Paramètres de pagination
export interface PaginationParams {
  pageNumber: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

// Réponse d'upload de fichier
export interface FileUploadResponse {
  fileName: string;
  filePath: string;
  fileSize: number;
  contentType: string;
  uploadDate: Date;
}

// Informations de fichier
export interface FileInfo {
  name: string;
  size: number;
  type: string;
  lastModified: Date;
  url?: string;
}

// Paramètres de recherche générique
export interface SearchParams {
  query?: string;
  filters?: { [key: string]: any };
  pagination?: PaginationParams;
}

// Résultat de recherche
export interface SearchResult<T> {
  items: T[];
  totalCount: number;
  searchTime: number; // en millisecondes
  suggestions?: string[];
}

// Interface pour les erreurs de validation
export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

// Interface pour les erreurs API
export interface ApiError {
  message: string;
  code?: string;
  details?: string;
  validationErrors?: ValidationError[];
  timestamp: Date;
}

// Interface pour les options de sélection (dropdowns, etc.)
export interface SelectOption<T = any> {
  value: T;
  label: string;
  disabled?: boolean;
  group?: string;
}

// Interface pour les filtres de date
export interface DateRangeFilter {
  startDate?: Date;
  endDate?: Date;
}

// Interface pour les statistiques génériques
export interface BaseStats {
  total: number;
  thisMonth: number;
  lastMonth: number;
  thisYear: number;
  lastYear: number;
  growthRate: number; // en pourcentage
}

// Interface pour les graphiques
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

// Interface pour les notifications toast
export interface ToastNotification {
  id?: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number; // en millisecondes
  persistent?: boolean;
}

// Interface pour les actions en lot
export interface BulkAction<T> {
  action: string;
  items: T[];
  parameters?: { [key: string]: any };
}

// Interface pour les résultats d'actions en lot
export interface BulkActionResult {
  successCount: number;
  errorCount: number;
  errors?: {
    item: any;
    error: string;
  }[];
}

// Interface pour l'export de données
export interface ExportOptions {
  format: 'excel' | 'csv' | 'pdf';
  fileName?: string;
  includeHeaders?: boolean;
  columns?: string[];
  filters?: { [key: string]: any };
}

// Interface pour les paramètres d'impression
export interface PrintOptions {
  orientation: 'portrait' | 'landscape';
  paperSize: 'A4' | 'A3' | 'Letter';
  includeHeader?: boolean;
  includeFooter?: boolean;
  margins?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}
