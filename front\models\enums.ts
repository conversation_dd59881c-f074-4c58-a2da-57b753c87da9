// ===== ENUMS =====

export enum StatutClient {
  Actif = 0,
  Inactif = 1,
  Suspendu = 2
}

export enum StatutFournisseur {
  Actif = 0,
  Inactif = 1,
  Suspendu = 2
}

export enum StatutFacture {
  Brouillon = 0,
  Envoyee = 1,
  Payee = 2,
  EnRetard = 3,
  Annulee = 4
}

export enum StatutDocument {
  EnAttente = 0,
  Approuve = 1,
  Rejete = 2
}

export enum TypeDemandeDocument {
  AttestationTravail = 0,
  AttestationStage = 1,
  CertificatTravail = 2,
  Autre = 3
}

export enum TypeNotification {
  FactureCree = 0,
  FactureStatutChange = 1,
  DocumentStatutChange = 2,
  DocumentDemande = 3,
  UtilisateurCree = 4,
  RetenueSourceAjoutee = 5,
  Systeme = 6
}

export enum StatutNotification {
  NonLue = 0,
  Lue = 1
}

// ===== HELPERS POUR LES ENUMS =====

export const StatutClientLabels = {
  [StatutClient.Actif]: 'Actif',
  [StatutClient.Inactif]: 'Inactif',
  [StatutClient.Suspendu]: 'Suspendu'
};

export const StatutFournisseurLabels = {
  [StatutFournisseur.Actif]: 'Actif',
  [StatutFournisseur.Inactif]: 'Inactif',
  [StatutFournisseur.Suspendu]: 'Suspendu'
};

export const StatutFactureLabels = {
  [StatutFacture.Brouillon]: 'Brouillon',
  [StatutFacture.Envoyee]: 'Envoyée',
  [StatutFacture.Payee]: 'Payée',
  [StatutFacture.EnRetard]: 'En retard',
  [StatutFacture.Annulee]: 'Annulée'
};

export const StatutDocumentLabels = {
  [StatutDocument.EnAttente]: 'En attente',
  [StatutDocument.Approuve]: 'Approuvé',
  [StatutDocument.Rejete]: 'Rejeté'
};

export const TypeDemandeDocumentLabels = {
  [TypeDemandeDocument.AttestationTravail]: 'Attestation de travail',
  [TypeDemandeDocument.AttestationStage]: 'Attestation de stage',
  [TypeDemandeDocument.CertificatTravail]: 'Certificat de travail',
  [TypeDemandeDocument.Autre]: 'Autre'
};

export const TypeNotificationLabels = {
  [TypeNotification.FactureCree]: 'Facture créée',
  [TypeNotification.FactureStatutChange]: 'Statut facture modifié',
  [TypeNotification.DocumentStatutChange]: 'Statut document modifié',
  [TypeNotification.DocumentDemande]: 'Demande de document',
  [TypeNotification.UtilisateurCree]: 'Utilisateur créé',
  [TypeNotification.RetenueSourceAjoutee]: 'Retenue à la source ajoutée',
  [TypeNotification.Systeme]: 'Notification système'
};

export const StatutNotificationLabels = {
  [StatutNotification.NonLue]: 'Non lue',
  [StatutNotification.Lue]: 'Lue'
};
