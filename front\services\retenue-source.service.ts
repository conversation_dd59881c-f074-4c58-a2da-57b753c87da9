import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { RetenueSource } from '../models';

@Injectable({
  providedIn: 'root'
})
export class RetenueSourceService {
  private readonly API_URL = '/api/retenues-source';

  constructor(private http: HttpClient) {}

  getRetenuesSource(): Observable<RetenueSource[]> {
    return this.http.get<RetenueSource[]>(this.API_URL);
  }

  getRetenueSource(id: string): Observable<RetenueSource> {
    return this.http.get<RetenueSource>(`${this.API_URL}/${id}`);
  }

  uploadRetenueSource(file: File, dateScan?: Date): Observable<RetenueSource> {
    const formData = new FormData();
    formData.append('fichier', file);
    
    if (dateScan) {
      formData.append('dateScan', dateScan.toISOString());
    }
    
    return this.http.post<RetenueSource>(this.API_URL, formData);
  }

  deleteRetenueSource(id: string): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/${id}`);
  }

  downloadFile(id: string): Observable<Blob> {
    return this.http.get(`${this.API_URL}/${id}/download`, { 
      responseType: 'blob' 
    });
  }

  scanDocument(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('fichier', file);
    
    return this.http.post(`${this.API_URL}/scan`, formData);
  }

  getRetenuesByDateRange(dateDebut: Date, dateFin: Date): Observable<RetenueSource[]> {
    const params = new HttpParams()
      .set('dateDebut', dateDebut.toISOString())
      .set('dateFin', dateFin.toISOString());
    
    return this.http.get<RetenueSource[]>(this.API_URL, { params });
  }

  getRetenuesByUtilisateur(utilisateurId: string): Observable<RetenueSource[]> {
    const params = new HttpParams().set('utilisateurId', utilisateurId);
    return this.http.get<RetenueSource[]>(this.API_URL, { params });
  }
}
