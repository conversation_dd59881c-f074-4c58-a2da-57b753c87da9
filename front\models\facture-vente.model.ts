import { FactureBase, FactureCreateBaseDto, FactureUpdateBaseDto } from './facture-base.model';
import { Client } from './client.model';

export interface FactureVente extends FactureBase {
  clientId: string;
  
  // Relations (optionnelles)
  client?: Client;
}

// DTO pour la création de facture de vente
export interface FactureVenteCreateDto extends FactureCreateBaseDto {
  clientId: string;
}

// DTO pour la modification de facture de vente
export interface FactureVenteUpdateDto extends FactureUpdateBaseDto {
  clientId: string;
}

// DTO pour les filtres spécifiques aux factures de vente
export interface FactureVenteFilterDto {
  numero?: string;
  clientId?: string;
  clientNom?: string;
  montantMin?: number;
  montantMax?: number;
  dateDebut?: Date;
  dateFin?: Date;
  statut?: number;
}

// Interface pour les statistiques spécifiques aux factures de vente
export interface FactureVenteStatsDto {
  totalFacturesVente: number;
  montantTotalVentes: number;
  chiffreAffairesMois: number;
  chiffreAffairesAnnee: number;
  clientLePlusActif: {
    id: string;
    nom: string;
    nombreFactures: number;
    montantTotal: number;
  };
}

// Interface pour le rapport de ventes
export interface RapportVentesDto {
  periode: {
    debut: Date;
    fin: Date;
  };
  ventesParMois: {
    mois: string;
    montant: number;
    nombreFactures: number;
  }[];
  ventesParClient: {
    clientId: string;
    clientNom: string;
    montant: number;
    nombreFactures: number;
  }[];
  evolutionChiffreAffaires: {
    date: Date;
    montantCumule: number;
  }[];
}
