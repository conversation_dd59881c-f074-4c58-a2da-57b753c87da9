import { Societe } from './societe.model';

export interface Applicationuser {
  id: string;
  nom: string;
  email: string;
  userName: string;
  societeId: string;
  societe?: Societe;
}

// DTO pour l'authentification
export interface UserLoginDto {
  email: string;
  motDePasse: string;
}

export interface UserRegisterDto {
  nom: string;
  email: string;
  motDePasse: string;
  confirmMotDePasse: string;
  // Informations société
  societeNom: string;
  societeAdresse: string;
  societeMatriculeFiscale: string;
  societeEmail?: string;
  societeTelephone?: string;
}

export interface UtilisateurDto {
  id: string;
  nom: string;
  email: string;
  role: string;
  societeId: string;
  societeNom?: string;
}

export interface UtilisateurCreateDto {
  nom: string;
  email: string;
  motDePasse: string;
  role: string; // "Admin" ou "User"
}

export interface UtilisateurUpdateDto {
  id: string;
  nom: string;
  email: string;
  role: string;
}

export interface AuthResponseDto {
  token: string;
  utilisateur: UtilisateurDto;
  expiresAt: Date;
}

// Interface pour le changement de mot de passe
export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

// Interface pour la réinitialisation de mot de passe
export interface ResetPasswordDto {
  email: string;
  token: string;
  newPassword: string;
  confirmNewPassword: string;
}
