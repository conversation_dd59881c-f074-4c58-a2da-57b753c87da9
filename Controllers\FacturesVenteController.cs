using Microsoft.AspNetCore.Mvc;
using stage.Models;
using stage.Repositories;
using stage.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;

namespace stage.Controllers
{
    [Route("api/factures-vente")]
    [ApiController]
    [Authorize] 
    public class FacturesVenteController : ControllerBase
    {
        private readonly IFactureVenteRepository _factureVenteRepository;
        private readonly IClientRepository _clientRepository;
        private readonly ISocieteRepository _societeRepository;
        private readonly IFileService _fileService;
        private readonly INotificationService _notificationService;

        public FacturesVenteController(
            IFactureVenteRepository factureVenteRepository,
            IClientRepository clientRepository,
            ISocieteRepository societeRepository,
            IFileService fileService,
            INotificationService notificationService)
        {
            _factureVenteRepository = factureVenteRepository;
            _clientRepository = clientRepository;
            _societeRepository = societeRepository;
            _fileService = fileService;
            _notificationService = notificationService;
        }

        private Guid GetUserSocieteId()
        {
            var societeIdClaim = User.FindFirst("SocieteId");
            if (societeIdClaim == null || !Guid.TryParse(societeIdClaim.Value, out var societeId))
            {
                throw new InvalidOperationException("SocieteId claim missing or invalid in JWT.");
            }
            return societeId;
        }

        private Guid GetUserId()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
            if (userIdClaim == null || !Guid.TryParse(userIdClaim.Value, out var userId))
            {
                throw new InvalidOperationException("UserId claim missing or invalid in JWT.");
            }
            return userId;
        }



        [HttpGet]
        public async Task<ActionResult<IEnumerable<FactureVente>>> GetFacturesVente()
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();



            var factures = await _factureVenteRepository.GetQueryable()
                .Include(f => f.Client)
                .Include(f => f.Societe)
                .Include(f => f.UtilisateurCreation)
                .Where(f => f.SocieteId == societeId)
                .OrderByDescending(f => f.DateCreation)
                .ToListAsync();

            return Ok(factures);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<FactureVente>> GetFactureVente(Guid id)
        {
            var societeId = GetUserSocieteId();
            var facture = await _factureVenteRepository.GetByIdAsync(id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            return Ok(facture);
        }

        [HttpPost]
        public async Task<ActionResult<FactureVente>> PostFactureVente([FromBody] FactureVente facture)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null || client.SocieteId != societeId)
            {
                return BadRequest("Le client spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            var societe = await _societeRepository.GetByIdAsync(societeId);
            if (societe == null)
            {
                return BadRequest("Société non trouvée.");
            }

            facture.Id = Guid.NewGuid();
            facture.SocieteId = societeId;
            facture.UtilisateurCreationId = userId;
            facture.Date = DateTime.UtcNow;
            facture.MatriculeFiscaleSociete = societe.MatriculeFiscale;

            await _factureVenteRepository.AddAsync(facture);
            await _factureVenteRepository.SaveChangesAsync();

            // Notification à tous les utilisateurs de la société
            await _notificationService.NotifyFactureCreatedAsync(userId, facture.Id, facture.Numero);

            var createdFacture = await _factureVenteRepository.GetByIdAsync(facture.Id);
            return CreatedAtAction(nameof(GetFactureVente), new { id = createdFacture.Id }, createdFacture);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> PutFactureVente(Guid id, [FromBody] FactureVente facture)
        {
            if (id != facture.Id || !ModelState.IsValid)
            {
                return BadRequest();
            }

            var societeId = GetUserSocieteId();
            var existingFacture = await _factureVenteRepository.GetByIdAsync(id);

            if (existingFacture == null || existingFacture.SocieteId != societeId)
            {
                return NotFound();
            }

            var client = await _clientRepository.GetByIdAsync(facture.ClientId);
            if (client == null || client.SocieteId != societeId)
            {
                return BadRequest("Le client spécifié n'existe pas ou n'appartient pas à votre société.");
            }

            existingFacture.Numero = facture.Numero;
            existingFacture.Montant = facture.Montant;
            existingFacture.ClientId = facture.ClientId;

            _factureVenteRepository.Update(existingFacture);

            try
            {
                await _factureVenteRepository.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!await FactureVenteExists(id, societeId))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteFactureVente(Guid id)
        {
            var societeId = GetUserSocieteId();
            var facture = await _factureVenteRepository.GetByIdAsync(id);

            if (facture == null || facture.SocieteId != societeId)
            {
                return NotFound();
            }

            _factureVenteRepository.Remove(facture);
            await _factureVenteRepository.SaveChangesAsync();

            return NoContent();
        }



        [HttpPost("{id}/change-status")]
        public async Task<IActionResult> ChangeStatus(Guid id, [FromBody] StatutFacture nouveauStatut)
        {
            var societeId = GetUserSocieteId();
            var userId = GetUserId();

            var facture = await _factureVenteRepository.GetByIdAsync(id);
            if (facture == null || facture.SocieteId != societeId)
                return NotFound();

            var ancienStatut = facture.Statut;
            facture.Statut = nouveauStatut;
            facture.DateModification = DateTime.UtcNow;

            // Mettre à jour les dates selon le statut
            switch (nouveauStatut)
            {
                case StatutFacture.Envoyee:
                    facture.DateEnvoi = DateTime.UtcNow;
                    break;
                case StatutFacture.Payee:
                    facture.DatePaiement = DateTime.UtcNow;
                    break;
            }

            _factureVenteRepository.Update(facture);
            await _factureVenteRepository.SaveChangesAsync();



            // Notification
            await _notificationService.NotifyFactureStatusChangedAsync(userId, facture.Id, facture.Numero, nouveauStatut);

            return Ok(new { Message = "Statut mis à jour avec succès", NouveauStatut = nouveauStatut });
        }





        private async Task<bool> FactureVenteExists(Guid id, Guid societeId)
        {
            return (await _factureVenteRepository.FindAsync(f => f.Id == id && f.SocieteId == societeId)).Any();
        }
    }
}
