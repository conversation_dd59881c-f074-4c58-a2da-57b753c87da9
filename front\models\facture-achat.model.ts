import { FactureBase, FactureCreateBaseDto, FactureUpdateBaseDto } from './facture-base.model';
import { Fournisseur } from './fournisseur.model';

export interface FactureAchat extends FactureBase {
  fournisseurId: string;
  
  // Propriétés pour l'upload de fichiers
  cheminFichier?: string;
  nomFichierOriginal?: string;
  tailleFichier?: number;
  typeFichier?: string;
  dateUpload?: Date;
  
  // Relations (optionnelles)
  fournisseur?: Fournisseur;
}

// DTO pour la création de facture d'achat
export interface FactureAchatCreateDto extends FactureCreateBaseDto {
  fournisseurId: string;
  fichier?: File;
}

// DTO pour la modification de facture d'achat
export interface FactureAchatUpdateDto extends FactureUpdateBaseDto {
  fournisseurId: string;
  fichier?: File;
}

// DTO pour l'upload de fichier sur une facture existante
export interface FactureAchatFileUploadDto {
  factureId: string;
  fichier: File;
}

// DTO pour les filtres spécifiques aux factures d'achat
export interface FactureAchatFilterDto {
  numero?: string;
  fournisseurId?: string;
  fournisseurNom?: string;
  montantMin?: number;
  montantMax?: number;
  dateDebut?: Date;
  dateFin?: Date;
  statut?: number;
  avecFichier?: boolean;
}

// Interface pour les informations de fichier
export interface FactureAchatFileInfo {
  nomFichierOriginal: string;
  tailleFichier: number;
  typeFichier: string;
  dateUpload: Date;
  urlTelechargement: string;
}

// Interface pour les statistiques spécifiques aux factures d'achat
export interface FactureAchatStatsDto {
  totalFacturesAchat: number;
  montantTotalAchats: number;
  facturesAvecFichier: number;
  facturesSansFichier: number;
  fournisseurLePlusActif: {
    id: string;
    nom: string;
    nombreFactures: number;
    montantTotal: number;
  };
}
