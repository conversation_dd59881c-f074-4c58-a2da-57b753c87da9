import { Applicationuser } from './user.model';
import { Client } from './client.model';
import { Fournisseur } from './fournisseur.model';
import { FactureAchat } from './facture-achat.model';
import { FactureVente } from './facture-vente.model';
import { Document } from './document.model';
import { RetenueSource } from './retenue-source.model';

export interface Societe {
  id: string;
  nom: string;
  adresse: string;
  matriculeFiscale: string;
  email?: string;
  telephone?: string;
  logo?: string;
  signature?: string;
  cachet?: string;
  
  // Relations (optionnelles pour éviter les références circulaires)
  utilisateurs?: Applicationuser[];
  clients?: Client[];
  fournisseurs?: Fournisseur[];
  facturesAchat?: FactureAchat[];
  facturesVente?: FactureVente[];
  documents?: Document[];
  retenuesSource?: RetenueSource[];
}

// DTO pour la création/modification de société
export interface SocieteCreateDto {
  nom: string;
  adresse: string;
  matriculeFiscale: string;
  email?: string;
  telephone?: string;
}

export interface SocieteUpdateDto {
  id: string;
  nom: string;
  adresse: string;
  matriculeFiscale: string;
  email?: string;
  telephone?: string;
  logo?: string;
  signature?: string;
  cachet?: string;
}

// DTO pour l'upload des éléments visuels
export interface SocieteUploadDto {
  logo?: File;
  signature?: File;
  cachet?: File;
}
